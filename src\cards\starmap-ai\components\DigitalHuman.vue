<script setup lang="ts">
// 数字人组件
</script>

<template>
  <div class="digital-human">
    <img 
      src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/szr.png" 
      alt="霖天科技AI助手" 
      class="digital-human-image mobile-image" 
    />
    <img 
      src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/szr-pc.jpeg" 
      alt="霖天科技AI助手" 
      class="digital-human-image desktop-image" 
    />
  </div>
</template>

<style scoped>
.digital-human {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.digital-human-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.desktop-image {
  display: block;
}

.mobile-image {
  display: block;
}

@media (max-width: 767px) {
  .desktop-image {
    display: none;
  }

  .mobile-image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

@media (min-width: 768px) {
  .digital-human {
    position: relative;
    width: 100vw;
    height: 100vh;
  }

  .desktop-image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    min-width: 100vw;
    min-height: 100vh;
  }

  .mobile-image {
    display: none;
  }
}
</style>
