import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'xingTuAIHome',
    component: () => import('./views/HomeView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/szr.png',
      title: '霖天科技AI名片'
    }
  },
  {
    path: '/project-intro',
    name: 'xingTuAIProjectIntro',
    component: () => import('./views/ProjectIntroView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/szr.png',
      title: '霖天科技AI名片 - 项目介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'xingTuAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: {
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/XingTuAI/szr.png',
      title: '霖天科技AI名片 - AI宣传员'
    }
  }
]

export default routes
