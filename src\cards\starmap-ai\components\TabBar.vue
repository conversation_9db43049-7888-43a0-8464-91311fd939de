<script setup lang="ts">
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { House, Collection } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const tabs = [
  {
    id: 'home',
    name: '主页',
    icon: House,
    route: '/card/starmap-ai'
  },
  {
    id: 'cases',
    name: '案例中心',
    icon: Collection,
    route: '/card/starmap-ai/ai-promoter'
  }
]

const activeTab = computed(() => {
  const currentPath = route.path
  if (currentPath === '/card/starmap-ai' || currentPath === '/card/starmap-ai/') {
    return 'home'
  } else if (currentPath.includes('/ai-promoter')) {
    return 'cases'
  }
  return 'home'
})

const handleTabClick = (tab: any) => {
  router.push(tab.route)
}
</script>

<template>
  <div class="tab-bar">
    <div 
      v-for="tab in tabs" 
      :key="tab.id" 
      class="tab-item"
      :class="{ active: activeTab === tab.id }"
      @click="handleTabClick(tab)"
    >
      <component :is="tab.icon" class="tab-icon" />
      <span class="tab-text">{{ tab.name }}</span>
    </div>
  </div>
</template>

<style scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(236, 205, 210, 0.3);
  display: flex;
  justify-content: space-around;
  padding: 0.5rem 0;
  z-index: 1000;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  min-width: 4rem;
}

.tab-item:hover {
  background: rgba(236, 205, 210, 0.1);
}

.tab-icon {
  font-size: 1.2rem;
  color: #9ca3af;
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.tab-text {
  font-size: 0.7rem;
  color: #9ca3af;
  transition: color 0.3s ease;
}

.tab-item.active .tab-icon,
.tab-item.active .tab-text {
  color: #d4a5b0;
}

.tab-item.active {
  background: rgba(236, 205, 210, 0.15);
}

@media (min-width: 768px) {
  .tab-bar {
    display: none;
  }
}
</style>
